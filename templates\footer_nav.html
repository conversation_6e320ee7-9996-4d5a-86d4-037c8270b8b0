<!-- Mobile Footer Navigation -->
<div class="footer-menu d-md-none">
    {% if role == "sublibrarian" %}
        <a href="/{{role}}/dashboard/" class="footer-link footer-dashboard">
            <div class="icon-text-wrapper">
                <i class="fas fa-tachometer-alt"></i>
                <span class="dashboard-text"><strong>Dashboard</strong></span>
            </div>
        </a>

        <a href="/students/create/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-circle-plus"></i>
                <span class="dashboard-text"><strong>Student Create</strong></span>
            </div>
        </a>

        <a href="/{{role}}/table/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-table"></i>
                <span class="dashboard-text"><strong>Table</strong></span>
            </div>
        </a>

        <a href="/visitors/create" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-user"></i>
                <span class="dashboard-text"><strong>Visitor Create</strong></span>
            </div>
        </a>

    {% elif role == "manager" %}
        <a href="/{{role}}/dashboard/" class="footer-link footer-dashboard">
            <div class="icon-text-wrapper">
                <i class="fas fa-tachometer-alt"></i>
                <span class="dashboard-text"><strong>Dashboard</strong></span>
            </div>
        </a>

        <a href="/{{role}}/table/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fas fa-table"></i>
                <span class="dashboard-text"><strong>Table</strong></span>
            </div>
        </a>

    {% elif role == "librarian" %}
        <a href="/{{role}}/dashboard/" class="footer-link footer-dashboard">
            <div class="icon-text-wrapper">
                <i class="fas fa-tachometer-alt"></i>
                <span class="dashboard-text"><strong>Dashboard</strong></span>
            </div>
        </a>

        <a href="/{{role}}/analytics/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fas fa-chart-line"></i>
                <span class="dashboard-text"><strong>Analytics</strong></span>
            </div>
        </a>

        <a href="/students/create/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-circle-plus"></i>
                <span class="dashboard-text"><strong>Student</strong></span>
            </div>
        </a>

        <a href="/students/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-users"></i>
                <span class="dashboard-text"><strong>Students</strong></span>
            </div>
        </a>

    {% elif role == "librarycommander" %}
        <a href="/{{role}}/dashboard/" class="footer-link footer-dashboard">
            <div class="icon-text-wrapper">
                <i class="fas fa-tachometer-alt"></i>
                <span class="dashboard-text"><strong>Dashboard</strong></span>
            </div>
        </a>

        <a href="/{{role}}/table/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-table"></i>
                <span class="dashboard-text"><strong>Table</strong></span>
            </div>
        </a>

        <a href="/blogs/create-blog/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fas fa-pencil-alt"></i>
                <span class="dashboard-text"><strong>Blogs</strong></span>
            </div>
        </a>

        <a href="/{{role}}/complaint_dashboard/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-exclamation-circle"></i>
                <span class="dashboard-text"><strong>Complaint</strong></span>
            </div>
        </a>
    {% endif %}

    <!-- Options menu with submenu -->
    <div class="settings-link footer-link" id="footer-settings">
        <div class="icon-text-wrapper">
            <i class="fa-solid fa-bars"></i>
            <span class="dashboard-text"><strong>Options</strong></span>
        </div>

        <div class="submenu" id="submenu">
            {% if role == "librarian" %}
                <a href="/{{role}}/profile/"><i class="fas fa-user"></i> View Profile</a>
                <a href="/{{role}}/table/"><i class="fa fa-table"></i> Table</a>
                <a href="/students/temp_students_list/"><i class="fas fa-book-reader"></i> Pending Students</a>
                <a href="/{{role}}/marketing/"><i class="bi bi-megaphone-fill"></i> Marketing Section</a>
                <a href="/visitors/"><i class="fa-solid fa-user"></i> Visitors Section</a>
                <a href="/{{role}}/daily-transaction/"><i class="fa-solid fa-money-bill"></i> Daily Transactions</a>
                <a href="/sublibrarian/signup/"><i class="fa-solid fa-user-plus"></i> Add Sub-librarian</a>
                <a href="/{{role}}/shifts/"><i class="fa-solid fa-table-list"></i> Manage Shifts</a>
                <a href="/{{role}}/seats/"><i class="fa-solid fa-chair"></i> Seats Control</a>
                <a href="/membership/plans/"><i class="fa-solid fa-cart-shopping"></i> Membership</a>
                <a href="/membership/sms-plans/"><i class="fa-solid fa-mobile-screen-button"></i> Sms Pack</a>
                <a href="/{{role}}/help/"><i class="bi bi-question-octagon-fill"></i> Help</a>
                <a href="/{{role}}/feedback/"><i class="bi bi-chat-left-text-fill"></i> Feedbacks</a>

            {% elif role == "sublibrarian" %}
                <a href="/students/"><i class="fa-solid fa-users"></i> Students Section</a>
                <a href="/{{role}}/table/"><i class="fa fa-table"></i> Table</a>
                <a href="/visitors/"><i class="fa-solid fa-user"></i> Visitors Section</a>

            {% elif role == "manager" %}
                <!-- Manager specific menu items can be added here -->

            {% elif role == "librarycommander" %}
                <a href="/{{role}}/dashboard/"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/{{role}}/table/"><i class="fa fa-table"></i> Table</a>
                <a href="/blogs/"><i class="fa fa-pencil-alt"></i> Blog Section</a>
                <a href="/{{role}}/complaint_dashboard/"><i class="fa fa-exclamation-circle"></i> Complaint Section</a>
                <a href="/{{role}}/logs/"><i class="fa fa-file-alt"></i> System Logs</a>
                <a href="/{{role}}/backups/"><i class="fa fa-download"></i> Download Database</a>
                <a href="/{{role}}/restore/"><i class="fa fa-undo"></i> Restore Database</a>
                <a href="/{{role}}/help/"><i class="bi bi-question-octagon-fill"></i> Help</a>
                <a href="/{{role}}/feedback/"><i class="bi bi-chat-left-text-fill"></i> Feedbacks</a>
            {% endif %}

            <!-- Logout link with modal trigger -->
            <a data-bs-toggle="modal" data-bs-target="#logoutModal"><i class="fas fa-sign-out-alt"></i> Logout</a>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Set active link in footer menu based on current path
        const currentPath = window.location.pathname;
        const footerLinks = document.querySelectorAll('.footer-link');

        footerLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }

            // Add click event to each footer link
            link.addEventListener('click', function() {
                footerLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Handle the options menu toggle
        const settingsLink = document.getElementById('footer-settings');
        const submenu = document.getElementById('submenu');

        if (settingsLink && submenu) {
            let isMenuOpen = false;

            settingsLink.addEventListener('click', function(e) {
                e.stopPropagation();
                isMenuOpen = !isMenuOpen;

                if (isMenuOpen) {
                    submenu.classList.add('open');
                    footerLinks.forEach(link => link.classList.remove('active'));
                    settingsLink.classList.add('active');
                } else {
                    submenu.classList.remove('open');
                    settingsLink.classList.remove('active');
                }
            });

            // Close submenu when clicking outside
            document.addEventListener('click', function(event) {
                if (isMenuOpen && !settingsLink.contains(event.target) && !submenu.contains(event.target)) {
                    submenu.classList.remove('open');
                    settingsLink.classList.remove('active');
                    isMenuOpen = false;
                }
            });

            // Prevent submenu clicks from closing the menu
            submenu.addEventListener('click', function(e) {
                e.stopPropagation();
            });

            // Close menu when clicking a submenu item
            submenu.querySelectorAll('a').forEach(link => {
                link.addEventListener('click', function() {
                    if (!this.hasAttribute('data-bs-toggle')) {
                        submenu.classList.remove('open');
                        settingsLink.classList.remove('active');
                        isMenuOpen = false;
                    }
                });
            });
        }
    });
</script>
