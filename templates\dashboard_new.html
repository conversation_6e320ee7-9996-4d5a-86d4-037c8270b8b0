{% extends "baseTemplate.html" %}

{% block title %}Dashboard - Librainian{% endblock %}

{% block meta_description %}Library Management Dashboard - Manage students, analytics, and daily operations{% endblock %}

{% block page_css %}
<!-- Dashboard specific CSS -->
<style>
    .dashboard_card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .dashboard_card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }

    .dashboard_card .icon {
        position: absolute;
        top: 15px;
        right: 15px;
        font-size: 2rem;
        opacity: 0.7;
    }

    .chart-container {
        background: white;
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-center">
    <div class="alert alert-success text-center" role="alert" style="width: 20rem;">
        LMS / DASHBOARD
    </div>
</div>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="card dashboard_card">
                <div class="card-body">
                    <h5 class="card-title">Students This Month</h5>
                    <p class="card-text">{{students_count}}</p>
                    <div class="icon"><i class="fas fa-users"></i></div>
                </div>
                <div class="card-footer">
                    <small class="text-light">Updated just now</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card dashboard_card">
                <div class="card-body">
                    <h5 class="card-title">Total Registrations</h5>
                    <p class="card-text">{{registration_count}}</p>
                    <div class="icon" style="font-size: 28px;"><i class="bi bi-currency-rupee"></i></div>
                </div>
                <div class="card-footer">
                    <small class="text-light">Updated just now</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card dashboard_card">
                <div class="card-body">
                    <h5 class="card-title">Total Revenue</h5>
                    <p class="card-text">₹{{total_revenue}}</p>
                    <div class="icon"><i class="fas fa-rupee-sign"></i></div>
                </div>
                <div class="card-footer">
                    <small class="text-light">Updated just now</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card dashboard_card">
                <div class="card-body">
                    <h5 class="card-title">Active Students</h5>
                    <p class="card-text">{{active_students}}</p>
                    <div class="icon"><i class="fas fa-user-check"></i></div>
                </div>
                <div class="card-footer">
                    <small class="text-light">Updated just now</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="chart-container">
                <h5>Monthly Sales</h5>
                <canvas id="monthlySalesChart"></canvas>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="chart-container">
                <h5>User Growth</h5>
                <canvas id="userGrowthChart"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block page_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- jQuery for legacy support -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function () {
        // Initialize Monthly Sales Chart
        var ctxMonthlySales = document.getElementById('monthlySalesChart').getContext('2d');
        var monthlySalesChart = new Chart(ctxMonthlySales, {
            type: 'line',
            data: {
                labels: {{ sales_months|safe }},
                datasets: [{
                    label: 'Sales Amount',
                    data: {{ sales_amounts|safe }},
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 2,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Initialize User Growth Chart
        var ctxUserGrowth = document.getElementById('userGrowthChart').getContext('2d');
        var userGrowthChart = new Chart(ctxUserGrowth, {
            type: 'bar',
            data: {
                labels: {{ growth_months|safe }},
                datasets: [{
                    label: 'User Count',
                    data: {{ user_counts|safe }},
                    backgroundColor: 'rgba(153, 102, 255, 0.2)',
                    borderColor: 'rgba(153, 102, 255, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });
</script>
{% endblock %}

<!-- Template Variables for Context -->
<!-- 
Expected context variables:
- role: User role (librarian, sublibrarian, etc.)
- students_count: Number of students this month
- registration_count: Total registrations
- total_revenue: Total revenue amount
- active_students: Number of active students
- sales_months: Array of month names for chart
- sales_amounts: Array of sales amounts for chart
- growth_months: Array of month names for growth chart
- user_counts: Array of user counts for growth chart
- show_sidebar: Boolean to show/hide sidebar (default: True)
- show_navbar: Boolean to show/hide navbar (default: True)
- show_mobile_footer: Boolean to show/hide mobile footer (default: True)
-->
