<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Dynamic Title Block -->
    <title>{% block title %}Librainian{% endblock %}</title>

     <!-- Google Tag Manager -->
      {% block google_tag_manager_head %}
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5L8WZWCC');</script>
    {% endblock google_tag_manager_head %}
    <!-- End Google Tag Manager --> 


    <!-- Meta Tags Block -->
    {% block meta %}
    <meta name="description" content="{% block meta_description %}Librainian - Modern Library Management System{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}library, management, system, students, books{% endblock %}">
    <meta name="author" content="{% block meta_author %}Librainian{% endblock %}">
    {% endblock meta %}

    <!-- Core CSS Dependencies -->
    {% block core_css %}
    <!-- Bootstrap 5.0.0 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css"
          integrity="sha384-wEmeIV1mKuiNpC+IOBjI7aAzPcEZeedi5yW5f2yOq55WWLwNGmvvx4Um1vskeMj0" crossorigin="anonymous">

    <!-- DataTables Bootstrap 5 -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">

    <!-- Icon Libraries -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

    <!-- Favicon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">
    {% endblock core_css %}

    <!-- Project CSS Files -->
    {% block project_css %}
    <link rel="stylesheet" href="/static/css/user_profile.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/analytics.css">
    <link rel="stylesheet" href="/static/css/marketing_page.css">
    <link rel="stylesheet" href="/static/css/daily_transactions.css">
    <link rel="stylesheet" href="/static/css/package.css">
    {% endblock project_css %}

    <!-- Global Styles -->
    {% block global_styles %}
    <style>
        /* Global Font Family */
        body h1, h2, h3, h4, h5, h6, p, ul, li, strong, em, b, s, small, span {
            font-family: 'Comfortaa', sans-serif !important;
        }

        /* Global Component Styling */
        .card {
            border-radius: 1rem;
        }

        table, .table {
            border-radius: 1rem !important;
        }

        .btn {
            border-radius: 1rem !important;
        }

        input {
            padding: 0.5rem 1rem;
            border-radius: 1rem !important;
            width: 90%;
        }

        select {
            padding: 0.5rem 1rem;
            border-radius: 1rem !important;
        }

        /* Mobile Responsive Utilities */
        @media only screen and (max-width: 767px) {
            .d-sm-none {
                display: none !important;
            }
        }

        /* Body Background */
        body {
            background: #cee3e0 !important;
            font-family: 'Comfortaa', sans-serif;
        }

        /* Navbar Styling */
        .navbar {
            margin: 0.5rem;
            position: sticky;
            top: 0.5rem;
            border-radius: 1rem;
            background: #fff !important;
            box-shadow: 0px 4px 6px 1px rgba(40, 52, 90, 0.3);
            z-index: 1000;
        }

        .navbar-brand {
            margin: 0 !important;
        }

        .nav_small {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* Footer Menu for Mobile */
        .footer-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            background-color: #f1f8ff;
            padding: 10px 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }

        .footer-menu a,
        .footer-menu .settings-link {
            color: #000000;
            font-size: 24px;
            text-align: center;
            text-decoration: none;
            position: relative;
        }

        .footer-menu a.active i,
        .footer-menu .settings-link.active i {
            color: #28345a;
        }

        /* Add animation for menu icon */
        .settings-link i.fa-bars {
            transition: transform 0.3s ease;
        }

        .settings-link.active i.fa-bars {
            transform: rotate(90deg);
        }

        /* Mobile submenu */
        .submenu {
            display: none;
            position: absolute;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(192, 221, 253, 0.8);
            z-index: 1001;
            left: 0;
            bottom: 60px;
            padding: 15px;
            border-radius: 10px;
            width: 280px;
            max-height: 400px;
            overflow-y: auto;
            transition: opacity 0.2s ease;
            opacity: 0;
            pointer-events: none;
        }

        /* Show submenu when open class is applied */
        .submenu.open {
            display: block;
            opacity: 1;
            pointer-events: auto;
        }

        .footer-menu .settings-link .submenu {
            left: -230px;
        }

        .footer-menu .settings-link a {
            display: block;
            color: #000000;
            padding: 8px 12px;
            text-decoration: none;
            text-align: start;
            margin-top: 5px;
            font-size: 0.8rem;
            border-radius: 5px;
        }

        .footer-menu .settings-link a:hover {
            background-color: #f0f0f0;
        }

        /* Icon text wrapper for footer menu */
        .icon-text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .icon-text-wrapper i {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .dashboard-text {
            font-size: 12px;
            line-height: 1.2;
        }

        /* Modal styling */
        .modal-content {
            border-radius: 1rem;
        }

        .btn-primary, .btn-secondary, .btn-success, .btn-danger {
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }

        /* Footer adjustments for mobile */
        @media (max-width: 768px) {
            .footer {
                margin-bottom: 100px;
            }
        }

        /* Profile dropdown styling */
        .profile-dropdown {
            width: 280px;
            max-width: 90vw;
            padding: 10px;
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: none;
            right: 0;
            left: auto !important;
            max-height: calc(100vh - 100px);
            overflow-y: auto;
        }

        /* Ensure dropdown is positioned correctly */
        .dropdown-menu-end {
            right: 0;
            left: auto !important;
        }

        .profile-dropdown .dropdown-item {
            border-radius: 5px;
            transition: all 0.2s ease;
            white-space: normal;
            padding: 0.5rem;
        }

        .profile-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .profile-dropdown .dropdown-item.text-danger:hover {
            background-color: #f8d7da;
        }

        .nav-item .nav-link {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.2s ease;
        }

        /* Profile image styling */
        .profile-img {
            width: 45px;
            height: 45px;
            object-fit: cover;
            border-radius: 50%;
            border: 2px solid #f1f1f1;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        /* Responsive adjustments for profile dropdown */
        @media (max-width: 576px) {
            .profile-dropdown {
                width: 250px;
                position: fixed !important;
                top: auto !important;
                bottom: 60px;
                right: 10px !important;
                left: auto !important;
            }

            .profile-img {
                width: 40px;
                height: 40px;
            }
        }
    </style>
    {% endblock global_styles %}

    <!-- Page Specific CSS -->
    {% block page_css %}
    {% endblock page_css %}

</head>

<body>
    <!-- Page Wrapper -->
    <div class="page-wrapper">

        
        <!-- Google Tag Manager (noscript) -->
        {% block google_tag_manager_body %}
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5L8WZWCC"
        height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        {% endblock google_tag_manager_body %}
        <!-- End Google Tag Manager (noscript) -->


        <!-- Navigation Block -->
        {% block navigation %}
        {% if show_sidebar %}
            {% include "sidebar.html" %}
        {% endif %}

        {% if show_navbar %}
            {% include "navbar.html" %}
        {% endif %}
        {% endblock navigation %}

        <!-- Main Content Area -->
        {% block main_content %}
        <div class="{% if show_sidebar %}main-content{% else %}container-fluid{% endif %}">

            <!-- Page Header Block -->
            {% block page_header %}
            {% endblock page_header %}

            <!-- Breadcrumb Block -->
            {% block breadcrumb %}
            {% endblock breadcrumb %}

            <!-- Alert Messages Block -->
            {% block messages %}
            {% if messages %}
                <div class="container-fluid mt-3">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            {% endblock messages %}

            <!-- Main Content Block -->
            {% block content %}
            {% endblock content %}

        </div>
        {% endblock main_content %}

        <!-- Mobile Footer Navigation -->
        {% block mobile_footer %}
        {% if show_mobile_footer %}
            {% include "footer_nav.html" %}
        {% endif %}
        {% endblock mobile_footer %}

    </div>

    <!-- Core JavaScript -->
    {% block core_js %}
    <!-- Bootstrap 5.0.0 JavaScript Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-p34f1UUtsS3wqzfto5wAAmdvj+osOnFyQFpp4Ua3gs/ZVWx6oOypYoCJhGGScy+8" crossorigin="anonymous"></script>
    {% endblock core_js %}

    <!-- Page Specific JavaScript -->
    {% block page_js %}
    {% endblock page_js %}

    <!-- Custom Scripts Block -->
    {% block scripts %}
    {% endblock scripts %}

    <!-- Analytics/Tracking Scripts -->
    {% block analytics %}

    {% endblock analytics %}

</body>
</html>