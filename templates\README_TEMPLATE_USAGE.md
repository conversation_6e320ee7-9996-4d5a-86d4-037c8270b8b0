# Librainian Template System Documentation

## Overview
The Librainian template system has been redesigned with proper Django template inheritance and modular components to reduce code duplication and improve maintainability.

## Template Structure

### 1. baseTemplate.html (Main Base Template)
The core template that all other templates should extend. It provides:
- Consistent HTML structure
- Bootstrap 5.0.0 integration
- Modular CSS and JavaScript blocks
- Navigation component integration
- Responsive design patterns

### 2. Component Templates
- **sidebar.html**: Desktop sidebar navigation
- **navbar.html**: Top navigation bar with profile dropdown
- **footer_nav.html**: Mobile bottom navigation

## How to Use

### Basic Template Extension
```django
{% extends "baseTemplate.html" %}

{% block title %}Your Page Title{% endblock %}

{% block content %}
    <!-- Your page content here -->
{% endblock %}
```

### Available Blocks

#### Head Section Blocks
- `title`: Page title (default: "Librainian")
- `meta`: Meta tags block
- `meta_description`: Page description
- `meta_keywords`: Page keywords
- `meta_author`: Page author
- `core_css`: Core CSS dependencies (Bootstrap, fonts, icons)
- `project_css`: Project-specific CSS files
- `global_styles`: Global styling (already includes common styles)
- `page_css`: Page-specific CSS

#### Body Section Blocks
- `navigation`: Navigation components (sidebar, navbar)
- `main_content`: Main content wrapper
- `page_header`: Page header section
- `breadcrumb`: Breadcrumb navigation
- `messages`: Django messages display
- `content`: Main page content
- `mobile_footer`: Mobile footer navigation

#### JavaScript Blocks
- `core_js`: Core JavaScript (Bootstrap)
- `page_js`: Page-specific JavaScript libraries
- `scripts`: Custom JavaScript code
- `analytics`: Analytics/tracking scripts

### Context Variables

#### Navigation Control
```python
context = {
    'show_sidebar': True,        # Show desktop sidebar
    'show_navbar': True,         # Show top navbar
    'show_mobile_footer': True,  # Show mobile footer navigation
    'role': 'librarian',         # User role for navigation
}
```

#### User Information
```python
context = {
    'user': request.user,        # Django user object
    'role': 'librarian',         # User role
}
```

### Example Templates

#### Dashboard Template
```django
{% extends "baseTemplate.html" %}

{% block title %}Dashboard - Librainian{% endblock %}

{% block page_css %}
<style>
    .dashboard-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
</style>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-center">
    <div class="alert alert-success text-center" role="alert">
        DASHBOARD
    </div>
</div>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Dashboard content -->
</div>
{% endblock %}

{% block scripts %}
<script>
    // Dashboard specific JavaScript
</script>
{% endblock %}
```

#### Form Template
```django
{% extends "baseTemplate.html" %}

{% block title %}Create Student - Librainian{% endblock %}

{% block page_css %}
<link rel="stylesheet" href="/static/css/forms.css">
{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/dashboard/">Dashboard</a></li>
        <li class="breadcrumb-item active">Create Student</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="container">
    <form method="post">
        {% csrf_token %}
        <!-- Form fields -->
    </form>
</div>
{% endblock %}
```

### Migration Guide

#### From Old Include Pattern
**Old way:**
```django
{% include "baseTemplate.html" %}
{% include "sidebar.html" %}
```

**New way:**
```django
{% extends "baseTemplate.html" %}
```

#### Context Updates
Ensure your views pass the required context variables:
```python
def dashboard_view(request):
    context = {
        'show_sidebar': True,
        'show_navbar': True,
        'show_mobile_footer': True,
        'role': request.user.role,
        # ... other context data
    }
    return render(request, 'dashboard.html', context)
```

### Best Practices

1. **Always extend baseTemplate.html** instead of including it
2. **Use specific blocks** for different types of content
3. **Keep page-specific CSS** in the `page_css` block
4. **Use semantic block names** for better organization
5. **Test responsive design** on both desktop and mobile
6. **Validate context variables** are passed correctly

### Troubleshooting

#### Common Issues
1. **Missing navigation**: Ensure context variables are set
2. **CSS conflicts**: Use specific selectors in page_css block
3. **JavaScript errors**: Check script loading order
4. **Mobile layout issues**: Test footer navigation visibility

#### Debug Tips
- Check browser console for JavaScript errors
- Validate HTML structure
- Test with different user roles
- Verify context variables in template debug

## Component Details

### Sidebar Navigation
- Automatically hidden on mobile devices
- Role-based menu items
- Active link highlighting
- Logout modal integration

### Top Navbar
- Profile dropdown with user information
- Responsive design
- Date display
- Logout functionality

### Mobile Footer Navigation
- Role-specific quick actions
- Expandable options menu
- Active state management
- Touch-friendly interface

This template system provides a solid foundation for consistent, maintainable, and responsive web pages throughout the Librainian application.
