<!-- Top Navbar Component -->
<nav class="navbar nav_small navbar-expand-lg navbar-light fixed-top">
    {% if role == "librarian" or role == "sublibrarian" %}
        <a class="navbar-brand" href="#">
            <img src="{% if request.user.librarian_param.image %}{{ request.user.librarian_param.image.url }}{% else %}https://picsum.photos/150{% endif %}"
                 alt="avatar" style="height: 50px; width: 250px;" loading="lazy">
        </a>
    {% else %}
        <a class="navbar-brand" href="#">
            <img src="/static/img/link_cover.jpg" alt="Default Logo" style="width: 220px;" loading="lazy">
        </a>
    {% endif %}

    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
            aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>

    <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ml-auto">
            <!-- Profile Dropdown -->
            <li class="nav-item dropdown">
                <a class="nav-link d-flex align-items-center" id="profileDropdown" role="button" 
                   data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <img src="/static/img/admin.png" alt="Profile" 
                         style="height: 35px; width: 35px; margin-right: 8px;" loading="lazy">
                    <span>Profile</span>
                </a>
                <div class="dropdown-menu dropdown-menu-end profile-dropdown" aria-labelledby="profileDropdown">
                    <div class="px-2 py-2">
                        <div class="d-flex align-items-center mb-2">
                            <img src="/static/img/admin.png" alt="Profile Image" class="profile-img me-2" loading="lazy">
                            <div style="font-size: 0.9rem;">
                                <h6 class="mb-0">{{user.first_name}} {{user.last_name}}</h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar-alt"></i> <span id="date"></span>
                                </small>
                            </div>
                        </div>

                        <div class="list-group list-group-flush mb-2">
                            <a href="/{{role}}/profile/" class="list-group-item list-group-item-action border-0 py-1 px-2">
                                <i class="fas fa-user me-2"></i> View Profile
                            </a>
                            <a href="/{{role}}/edit-profile/" class="list-group-item list-group-item-action border-0 py-1 px-2">
                                <i class="fas fa-edit me-2"></i> Edit Profile
                            </a>
                        </div>

                        <div class="d-grid gap-2">
                            <a data-bs-toggle="modal" data-bs-target="#logoutModal" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-sign-out-alt me-2"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</nav>

<!-- Logout Modal -->
<div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="logoutModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="logoutModalLabel">Logout Confirmation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to logout?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
                <a href="/{{role}}/logout" class="btn btn-primary">Yes</a>
            </div>
        </div>
    </div>
</div>

<script>
    // Update date and time in navbar
    function updateDateTime() {
        const now = new Date();
        const day = String(now.getDate()).padStart(2, '0');
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const year = String(now.getFullYear()).slice(-2);
        const date = `${day}/${month}/${year}`;

        const dateElement = document.getElementById('date');
        if (dateElement) {
            dateElement.textContent = date;
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        updateDateTime();
        setInterval(updateDateTime, 60000); // Update every minute
    });
</script>
