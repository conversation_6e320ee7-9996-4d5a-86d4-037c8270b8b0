{% extends "baseTemplate.html" %}

{% block title %}Page Not Found | Librainian{% endblock %}

{% block meta %}
    <meta name="description" content="Page Not Found | Librainian - The #1 Library Management System. Return to our homepage to explore our library management tools.">
    <meta name="keywords" content="librainian, library management system, lms, page not found, 404 error, library software, library management tool">
    <meta name="robots" content="noindex, follow">
    <link rel="canonical" href="https://www.librainian.com/404">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="content-language" content="en">
    <meta name="geo.region" content="IN">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Librainian">
    <meta property="og:title" content="Page Not Found | Librainian">
    <meta property="og:description" content="The page you're looking for doesn't exist. Return to our homepage to explore our library management tools.">
    <meta property="og:url" content="https://www.librainian.com/404">
    <meta property="og:image" content="https://www.librainian.com/static/img/error.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:title" content="Page Not Found | Librainian">
    <meta name="twitter:description" content="The page you're looking for doesn't exist. Return to our homepage to explore our library management tools.">
    <meta name="twitter:image" content="https://www.librainian.com/static/img/error.jpg">

    <!-- Author and Date Info -->
    <meta itemprop="author" content="Librainian Team">
    <meta itemprop="datePublished" content="2024-07-01">
    <meta itemprop="dateModified" content="2024-07-07">
{% endblock meta %}

{% block core_css %}
    {{ block.super }}
    <!-- Favicon override for 404 page -->
    <link href="https://i.postimg.cc/ZY3b6dw9/librainian-logo-black-transparent-med.png" rel="icon">

{% endblock core_css %}

{% block page_js %}
    <script>
        // Inline loader script to show loader immediately when page starts loading
        (function() {
            // Create loader HTML with FSEX300 font and LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                    <style>
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('/static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                        }

                        @keyframes blink {
                            0%, 100% { opacity: 1; }
                            50% { opacity: 0; }
                        }

                        @keyframes dots {
                            0% { content: ""; }
                            25% { content: "."; }
                            50% { content: ".."; }
                            75% { content: "..."; }
                            100% { content: ""; }
                        }

                        .loader-text {
                            font-family: 'FSEX300', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            margin-bottom: 20px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                        }

                        .loader-dots::after {
                            content: "";
                            animation: dots 1.5s infinite;
                        }

                        .loader-bar {
                            width: 300px;
                            height: 20px;
                            background-color: rgba(255, 255, 255, 0.2);
                            border-radius: 10px;
                            overflow: hidden;
                            margin: 20px auto;
                        }

                        .loader-progress {
                            width: 0%;
                            height: 100%;
                            background-color: #6200ee;
                            border-radius: 10px;
                            animation: progress 2s infinite;
                        }

                        @keyframes progress {
                            0% { width: 0%; }
                            50% { width: 100%; }
                            100% { width: 0%; }
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN<span class="loader-dots"></span></div>
                    <div class="loader-bar">
                        <div class="loader-progress"></div>
                    </div>
                </div>
            </div>
            `;

            // Add loader to page
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
            });
        })();
    </script>
{% endblock page_js %}

{% block analytics %}
    <!-- Structured Data / JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Page Not Found | Librainian",
        "description": "The page you're looking for doesn't exist. Return to our homepage to explore our library management tools.",
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://www.librainian.com/static/img/librainian-logo-black-transparent.png"
            }
        },
        "isPartOf": {
            "@type": "WebSite",
            "name": "Librainian",
            "url": "https://www.librainian.com/"
        },
        "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "https://www.librainian.com/"
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "Page Not Found",
                    "item": "https://www.librainian.com/404"
                }
            ]
        }
    }
    </script>
{% endblock analytics %}

{% block page_css %}
    <style>
        /* Bootstrap 5.0.0 compatible styles with project consistency */
        :root {
            --primary-color: #294282;
            --secondary-color: #9bc6bf;
            --error-color: #dc3545;
            --text-color: #343a40;
            --shadow-light: 0 4px 20px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.15);
            --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Override base template body styles for 404 page */
        body {
            -webkit-user-select: none !important; /* Disable text selection */
            -moz-user-select: none !important;
            -ms-user-select: none !important;
            user-select: none !important;
            margin: 0 !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            min-height: 100vh !important;
            font-family: 'Comfortaa', sans-serif !important;
            padding: 1rem !important;
        }

        /* Hide navigation components for 404 page */
        .sidebar, .navbar, .footer-menu, .main-content {
            display: none !important;
        }

        .error-container {
            text-align: center;
            max-width: 700px;
            padding: 3rem;
            border-radius: 2rem;
            background: #ffffff;
            box-shadow: var(--shadow-medium);
            border: 1px solid #e9ecef;
            transition: var(--transition-smooth);
        }

        .error-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        }

        .error-image {
            max-width: 100%;
            height: auto;
            border-radius: 1rem;
            margin-bottom: 2rem;
            transition: var(--transition-smooth);
        }

        .error-image:hover {
            transform: scale(1.02);
        }

        .error-code {
            font-size: 6rem;
            font-weight: 700;
            color: var(--error-color);
            margin: 1rem 0;
            text-shadow: 2px 2px 4px rgba(220, 53, 69, 0.2);
            font-family: 'Comfortaa', sans-serif !important;
        }

        .error-message {
            font-size: 1.5rem;
            margin: 1.5rem 0;
            color: var(--text-color);
            font-weight: 500;
            line-height: 1.6;
        }

        .home-button {
            margin-top: 2rem;
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            border-radius: 1rem !important;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            transition: var(--transition-smooth);
            text-decoration: none;
        }

        .home-button:hover {
            background-color: #1e3a73 !important;
            border-color: #1e3a73 !important;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(41, 66, 130, 0.3);
        }

        .home-button:focus {
            box-shadow: 0 0 0 0.25rem rgba(41, 66, 130, 0.25);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .error-container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }

            .error-code {
                font-size: 4rem;
            }

            .error-message {
                font-size: 1.2rem;
            }

            .home-button {
                padding: 0.6rem 1.5rem;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .error-container {
                padding: 1.5rem 1rem;
            }

            .error-code {
                font-size: 3rem;
            }

            .error-message {
                font-size: 1rem;
            }
        }

        /* Animation for error code */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.25); }
            100% { transform: scale(1); }
        }

        .error-code {
            animation: pulse 2s ease-in-out infinite;
        }

        /* Additional styling for better UX */
        .error-subtitle {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 1rem;
            font-weight: 400;
        }
    </style>
{% endblock page_css %}

{% block navigation %}
    <!-- Override navigation block to hide all navigation for 404 page -->
{% endblock navigation %}

{% block main_content %}
    <!-- Override main_content to remove wrapper for 404 page -->
    <div class="error-container">
        <img src="/static/img/error.jpg" alt="Error 404 - Page Not Found" class="error-image" loading="lazy">
        <div class="error-code">404</div>
        <div class="error-subtitle">Page Not Found</div>
        <div class="error-message">Oops! The page you are looking for does not exist.</div>
        <p class="text-muted mb-4">The page might have been moved, deleted, or you entered the wrong URL.</p>

        {% if role %}
        <a href="/{{role}}/dashboard/" class="btn btn-primary home-button">
            <i class="bi bi-house-door me-2"></i>Return to Dashboard
        </a>
        {% else %}
        <a href="/" class="btn btn-primary home-button">
            <i class="bi bi-house-door me-2"></i>Return to Home Page
        </a>
        {% endif %}

        <div class="mt-4">
            <a href="javascript:history.back()" class="btn btn-outline-secondary" style="border-radius: 1rem;">
                <i class="bi bi-arrow-left me-2"></i>Go Back
            </a>
        </div>
    </div>
{% endblock main_content %}

{% block mobile_footer %}
    <!-- Override mobile_footer to hide for 404 page -->
{% endblock mobile_footer %}

{% block scripts %}
    <!-- Bootstrap 5.0.0 initialization check and 404-specific JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof bootstrap !== 'undefined') {
                console.log('Bootstrap 5.0.0 loaded successfully for 404 page');
                console.log('Bootstrap version:', bootstrap.Modal.VERSION || 'Version not available');
            } else {
                console.error('Bootstrap is not loaded!');
            }

            // Add some interactive behavior
            const errorContainer = document.querySelector('.error-container');
            const errorCode = document.querySelector('.error-code');

            // Add click event to error code for fun interaction
            if (errorCode) {
                errorCode.addEventListener('click', function() {
                    this.style.animation = 'none';
                    setTimeout(() => {
                        this.style.animation = 'pulse 2s ease-in-out infinite';
                    }, 100);
                });
            }

            // Track 404 errors (if analytics is available)
            if (typeof gtag !== 'undefined') {
                gtag('event', 'page_view', {
                    page_title: '404 Error',
                    page_location: window.location.href,
                    custom_map: {'custom_parameter': 'error_page'}
                });
            }
        });
    </script>
{% endblock scripts %}