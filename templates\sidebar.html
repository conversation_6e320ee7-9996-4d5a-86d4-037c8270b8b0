
<!-- Sidebar Styles -->
<style>
    /* Sidebar Core Styles */
    .sidebar {
        position: fixed;
        top: 0.5rem;
        left: 0.5rem;
        height: 97vh;
        width: 250px;
        padding-top: 20px;
        margin-bottom: 3rem;
        transition: all 0.3s ease;
        border-radius: 1rem;
        background-color: #28345a;
        color: #fff;
        /* Unified scrollbar handling */
        overflow-y: auto;
        overflow-x: hidden;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        z-index: 1010;
    }

    /* Hide scrollbar for Chrome, Safari and Opera */
    .sidebar::-webkit-scrollbar {
        display: none;
    }

    /* Dashboard heading */
    .dashboard_heading {
        padding: 0.5rem 1rem;
        margin: 0rem 1rem 1rem 1rem;
        border-radius: 1rem;
        background: #6a9a9e;
        max-width: 13rem;
        font-weight: bold;
    }

    /* Sidebar links */
    .sidebar a {
        color: #fff;
        padding: 12px 15px 12px 25px;
        display: block;
        text-decoration: none;
        border-left: 3px solid transparent;
        transition: all 0.2s ease;
        margin-bottom: 2px;
    }

    .sidebar a:hover {
        background-color: rgba(73, 80, 87, 0.7);
        border-left: 3px solid #007bff;
    }

    .sidebar a.active {
        background-color: #9bc6bf;
        color: white;
        border-left: 3px solid #007bff;
    }

    /* Main content adjustment */
    .main-content {
        margin-left: 250px;
        padding: 4px;
        transition: all 0.3s;
    }

    /* Mobile adjustments */
    @media (max-width: 768px) {
        .sidebar {
            display: none; /* Hidden on mobile */
        }

        .main-content {
            margin-left: 0;
        }
    }


</style>


<div class="sidebar">
    <h6 class="text-center dashboard_heading">{{ role|capfirst }} Dashboard</h6>

    {% if role == "librarian" %}
    <a href="/{{role}}/dashboard/" onclick="setActiveLink(this)"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
    <a href="/{{role}}/analytics/" onclick="setActiveLink(this)"><i class="fas fa-chart-line"></i> Analytics</a>
    <a href="/{{role}}/table/" onclick="setActiveLink(this)"><i class="fa fa-table"></i> Table</a>
    <a href="/students/temp_students_list/" onclick="setActiveLink(this)"><i class="fas fa-book-reader"></i> Pending Students</a>
    <a href="/students/" onclick="setActiveLink(this)"><i class="fa-solid fa-users"></i> Students Section</a>
    <a href="/{{role}}/marketing/" onclick="setActiveLink(this)"><i class="bi bi-megaphone-fill"></i> Marketing Section</a>
    <a href="/visitors/" onclick="setActiveLink(this)"><i class="fa-solid fa-user"></i> Visitors Section</a>
    <a href="/{{role}}/daily-transaction/" onclick="setActiveLink(this)"><i class="fa-solid fa-money-bill"></i> Daily Transactions</a>
    <a href="/sublibrarian/signup/" onclick="setActiveLink(this)"><i class="fa-solid fa-user-plus"></i> Add Sublibrarian</a>
    <a href="/{{role}}/shifts/" onclick="setActiveLink(this)"><i class="fa-solid fa-table-list"></i> Manage Shifts</a>
    <a href="/{{role}}/seats/" onclick="setActiveLink(this)"><i class="fa-solid fa-chair"></i> Seats Control</a>
    <a href="/membership/plans/" onclick="setActiveLink(this)"><i class="fa-solid fa-cart-shopping"></i> Membership</a>
    <a href="/membership/sms-plans/" onclick="setActiveLink(this)"><i class="fa-solid fa-mobile-screen-button"></i> Sms Pack</a>
    <a href="/{{role}}/help/" onclick="setActiveLink(this)"><i class="bi bi-question-octagon-fill"></i> Help</a>
    <a href="/{{role}}/feedback/" onclick="setActiveLink(this)"><i class="bi bi-chat-left-text-fill"></i> Feedbacks</a>

    {% elif role == "sublibrarian" %}
    <a href="/{{role}}/dashboard/" onclick="setActiveLink(this)"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
    <a href="/students/" onclick="setActiveLink(this)"><i class="fa-solid fa-users"></i> Students Section</a>
    <a href="/{{role}}/table/" onclick="setActiveLink(this)"><i class="fa fa-table"></i> Table</a>
    <a href="/students/temp_subLib_students_list/" onclick="setActiveLink(this)"><i class="fas fa-book-reader"></i> Pending Students</a>
    <a href="/{{role}}/daily-transaction/" onclick="setActiveLink(this)"><i class="fa-solid fa-money-bill"></i> Daily Transactions</a>
    <a href="/membership/plans/" onclick="setActiveLink(this)"><i class="fa-solid fa-cart-shopping"></i> Membership</a>
    <a href="/membership/sms-plans/" onclick="setActiveLink(this)"><i class="fa-solid fa-mobile-screen-button"></i> Sms Pack</a>
    <a href="/visitors/" onclick="setActiveLink(this)"><i class="fa-solid fa-user"></i> Visitors Section</a>
    <a href="/{{role}}/shifts/" onclick="setActiveLink(this)"><i class="fa-solid fa-table-list"></i> Manage Shifts</a>
    <a href="/{{role}}/seats/" onclick="setActiveLink(this)"><i class="fa-solid fa-chair"></i> Seats Control</a>
    <a href="/{{role}}/help/" onclick="setActiveLink(this)"><i class="bi bi-question-octagon-fill"></i> Help</a>
    <a href="/{{role}}/feedback/" onclick="setActiveLink(this)"><i class="bi bi-chat-left-text-fill"></i> Feedbacks</a>

    {% elif role == "manager" %}
    <a href="/{{role}}/dashboard/" onclick="setActiveLink(this)"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
    <a href="/{{role}}/table/" onclick="setActiveLink(this)"><i class="fa fa-table"></i> Table</a>

    {% elif role == "librarycommander" %}
    <a href="/{{role}}/dashboard/" onclick="setActiveLink(this)"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
    <a href="/{{role}}/table/" onclick="setActiveLink(this)"><i class="fa fa-table"></i> Table</a>
    <a href="/blogs/coupons/create_coupon/" onclick="setActiveLink(this)"><i class="fa fa-ticket-alt"></i> Coupon</a>
    <a href="/blogs/" onclick="setActiveLink(this)"><i class="fa fa-pencil-alt"></i> Blog Section</a>
    <a href="/{{role}}/complaint_dashboard/" onclick="setActiveLink(this)"><i class="fa fa-exclamation-circle"></i> Complaint Section</a>
    <a href="/{{role}}/logs/" onclick="setActiveLink(this)"><i class="fa fa-file-alt"></i> System Logs</a>
    <a href="/{{role}}/backups/" onclick="setActiveLink(this)"><i class="fa fa-download"></i> Download Database</a>
    <a href="/{{role}}/restore/" onclick="setActiveLink(this)"><i class="fa fa-undo"></i> Restore Database</a>
    <a href="/{{role}}/help/" onclick="setActiveLink(this)"><i class="bi bi-question-octagon-fill"></i> Help</a>
    <a href="/{{role}}/feedback/" onclick="setActiveLink(this)"><i class="bi bi-chat-left-text-fill"></i> Feedbacks</a>
    {% endif %}


    <!-- Logout button with modal trigger -->
    <a data-bs-toggle="modal" data-bs-target="#logoutModal"><i class="fas fa-sign-out-alt"></i> Logout</a>
</div>

<!-- Logout Modal -->
<div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="logoutModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="logoutModalLabel">Logout Confirmation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to logout?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
                <a href="/{{role}}/logout" class="btn btn-primary">Yes</a>
            </div>
        </div>
    </div>
</div>

<script>
    // Function to set active class for sidebar links
    function setActiveLink(link) {
        // Remove active class from all sidebar links
        let sidebarLinks = document.querySelectorAll('.sidebar a');
        sidebarLinks.forEach(sidebarLink => {
            sidebarLink.classList.remove('active');
        });

        // Add active class to clicked link
        link.classList.add('active');

        // Store the active link in localStorage
        localStorage.setItem('activeLink', link.getAttribute('href'));
    }

    // Set active link in sidebar based on localStorage or current path
    document.addEventListener('DOMContentLoaded', function () {
        const currentPath = window.location.pathname;
        const sidebarLinks = document.querySelectorAll('.sidebar a');
        const savedActiveLink = localStorage.getItem('activeLink');

        if (savedActiveLink) {
            sidebarLinks.forEach(link => {
                if (link.getAttribute('href') === savedActiveLink) {
                    link.classList.add('active');
                }
            });
        } else {
            // If no saved link, set active based on current path
            sidebarLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        }
    });
</script>
